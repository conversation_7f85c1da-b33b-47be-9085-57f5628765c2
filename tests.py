import sys

from freezegun import freeze_time
import pytest
from greqs import main
from greqs.helper import file_template, find_module_spec, iter_import_modules
from inspect import cleandoc

sys.path.insert(0, "example")


def test_mod1():
    assert main(["mod1"]) == [
        "Flask==3.0.3",
        "git+https://github.com/Dog-Egg/Zangar",
        "git+https://github.com/Dog-Egg/oasis.git#subdirectory=packages/flask-oasis",
        "lxml",
        "psycopg[binary]",
        "requests",
        "six",
    ]


def test_pkg1():
    assert main(["pkg1"]) == ["requests", "six"]


def test_pkg2():
    assert main(["pkg2"]) == ["admin-deps"]


def test_find_module_spec():
    with pytest.raises(ModuleNotFoundError) as e:
        find_module_spec("xxx", True)
    assert e.value.msg == "No module named 'xxx'"

    with pytest.raises(ModuleNotFoundError) as e:
        find_module_spec("xxx.yyy", True)
    assert e.value.msg == "No module named 'xxx'"


def test_cli():
    # 测试命令行
    from greqs import cli

    cli.main(["--verbose", "mod1"])


def test_iter_import_modules():
    assert (
        list(
            iter_import_modules(
                cleandoc(
                    """
        import os
        from . import utils
        from .tools import translations
        from otherpkg import *
    """
                ),
                "package.module",
            )
        )
        == [
            ("os", True),
            ("package", True),
            ("package.module", True),
            ("package.module.utils", False),
            ("package.tools", True),
            ("package.tools.translations", False),
            ("otherpkg", True),
        ]
    )


@freeze_time("2025-01-01")
def test_file_template():
    assert file_template("Hello") == (
        """# Generated by https://github.com/Dog-Egg/greqs on 2025-01-01 00:00\n\nHello\n"""
    )
