from __future__ import annotations

import ast
import datetime
from importlib.machinery import ModuleSpec
import importlib.util
import os
import typing


def _iter_import_modules(
    code: str, module: str
) -> typing.Generator[tuple[str, bool], None, None]:
    """
    从代码中获取可能的导入模块名 (有可能导入的是对象，但该函数不进行分辨)

    @param code: 模块代码
    @param module: 模块名
    @return: (module_name, definite_module) definite_module 为 True 表示一定是模块，为 False 表示可能是模块。
    """
    tree = ast.parse(code)
    for node in ast.walk(tree):
        if isinstance(node, ast.ImportFrom):
            if node.level == 0:
                assert node.module is not None
                parent_name = node.module
            else:
                name = "." * node.level
                if node.module:
                    name = name + "." + node.module
                parent_name = importlib.util.resolve_name(name, module)

            # 当导入一个 pkg.mod 的模块时，必然会导入 pkg.__init__ (也就是 pkg)，所以需要将模块名逐级展开。
            for n in _expand_module_name(parent_name):
                yield n, True

            for n in node.names:
                if n.name != "*":
                    yield f"{parent_name}.{n.name}", False

        elif isinstance(node, ast.Import):
            for n in node.names:
                yield n.name, True


def iter_import_modules(
    *args, **kwargs
) -> typing.Generator[tuple[str, bool], None, None]:
    unique = set()
    for item in _iter_import_modules(*args, **kwargs):
        if item not in unique:
            unique.add(item)
            yield item


def _expand_module_name(name: str) -> list[str]:
    parts = name.split(".")
    return [".".join(parts[: i + 1]) for i in range(len(parts))]


def file_template(content: str):
    return os.linesep.join(
        [
            f'# Generated by https://github.com/Dog-Egg/greqs on {datetime.datetime.now().strftime("%Y-%m-%d %H:%M")}',
            "",
            content,
            "",
        ]
    )


def find_module_spec(name: str, definite_module: bool) -> ModuleSpec | None:
    try:
        spec = importlib.util.find_spec(name)
    except ModuleNotFoundError:
        if definite_module:
            raise
    else:
        if spec is None:
            if definite_module:
                raise ModuleNotFoundError(f"No module named {name!r}")
        return spec
